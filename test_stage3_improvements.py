#!/usr/bin/env python3
"""
Тестовый скрипт для проверки улучшений этапа 3:
- Обработка исключений в критических местах
- Улучшенное логирование и метрики
- Graceful degradation
"""

import sys
import time
import threading
from unittest.mock import Mock, patch

# Добавляем текущую директорию в путь для импорта модулей
sys.path.insert(0, '.')

def test_bot_globals_metrics():
    """Тестирует новые метрики в bot_globals.py"""
    print("=== Тестирование метрик bot_globals ===")
    
    try:
        from bot_globals import (
            get_bot_api_metrics, 
            reset_bot_api_metrics,
            _update_bot_api_metrics,
            _log_bot_api_metrics
        )
        
        # Сбрасываем метрики
        reset_bot_api_metrics()
        
        # Тестируем обновление метрик
        _update_bot_api_metrics('total_calls', 5)
        _update_bot_api_metrics('successful_calls', 3)
        _update_bot_api_metrics('failed_calls', 2)
        _update_bot_api_metrics('cache_hits', 10)
        _update_bot_api_metrics('cache_misses', 2)
        _update_bot_api_metrics('timeout_errors', 1)
        
        # Получаем метрики
        metrics = get_bot_api_metrics()
        
        print(f"✅ Метрики успешно обновлены:")
        print(f"   Total calls: {metrics['total_calls']}")
        print(f"   Success rate: {metrics['success_rate']:.1f}%")
        print(f"   Cache hit rate: {metrics['cache_hit_rate']:.1f}%")
        print(f"   Timeout errors: {metrics['timeout_errors']}")
        
        # Тестируем логирование метрик
        print("✅ Тестируем логирование метрик:")
        _log_bot_api_metrics()
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании метрик: {e}")
        return False

def test_get_bot_id_error_handling():
    """Тестирует обработку ошибок в get_bot_id()"""
    print("\n=== Тестирование обработки ошибок get_bot_id ===")
    
    try:
        from bot_globals import get_bot_id, get_bot_username
        
        # Тестируем нормальный вызов
        bot_id = get_bot_id()
        bot_username = get_bot_username()
        
        print(f"✅ get_bot_id() вернул: {bot_id}")
        print(f"✅ get_bot_username() вернул: {bot_username}")
        
        # Проверяем что функции возвращают fallback значения при ошибках
        if bot_id == 0:
            print("✅ Fallback значение для bot_id работает корректно")
        
        if bot_username == 'unknown_bot':
            print("✅ Fallback значение для bot_username работает корректно")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании get_bot_id: {e}")
        return False

def test_rename_tool_error_handling():
    """Тестирует обработку ошибок в rename_tool.py"""
    print("\n=== Тестирование обработки ошибок rename_tool ===")
    
    try:
        from rename_tool import process_rename_tool, check_rename_permissions
        
        # Создаем мок объект сообщения
        mock_message = Mock()
        mock_message.chat.type = 'supergroup'
        mock_message.chat.id = -1001234567890
        mock_message.from_user.id = 123456789
        
        # Тестируем process_rename_tool с [RENAME:] тегом
        test_response = "Привет! [RENAME:Тестовый админ] Как дела?"
        
        print("✅ Тестируем process_rename_tool...")
        cleaned_response = process_rename_tool(test_response, mock_message)
        
        # Проверяем что [RENAME:] тег удален
        if "[RENAME:" not in cleaned_response:
            print("✅ [RENAME:] тег успешно удален из ответа")
        else:
            print("❌ [RENAME:] тег не был удален")
        
        # Тестируем check_rename_permissions
        print("✅ Тестируем check_rename_permissions...")
        permissions = check_rename_permissions(mock_message)
        
        print(f"   Результат проверки прав: {permissions}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании rename_tool: {e}")
        return False

def test_imports():
    """Тестирует что все импорты работают корректно"""
    print("\n=== Тестирование импортов ===")
    
    try:
        # Тестируем импорт основных модулей
        import bot_globals
        print("✅ bot_globals импортирован успешно")
        
        import handlers
        print("✅ handlers импортирован успешно")
        
        import main
        print("✅ main импортирован успешно")
        
        import rename_tool
        print("✅ rename_tool импортирован успешно")
        
        # Тестируем импорт новых функций
        from bot_globals import get_bot_id, get_bot_username, get_bot_api_metrics
        print("✅ Новые функции bot_globals импортированы успешно")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при импорте: {e}")
        return False

def main():
    """Основная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ УЛУЧШЕНИЙ ЭТАПА 3")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_bot_globals_metrics,
        test_get_bot_id_error_handling,
        test_rename_tool_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Критическая ошибка в тесте {test.__name__}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ: {passed}/{total} тестов пройдено")
    
    if passed == total:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
        print("✅ Этап 3 готов к продакшену")
    else:
        print("⚠️  Некоторые тесты не прошли, требуется дополнительная проверка")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
