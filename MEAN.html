<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>8-Bit Prompt</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
<style>
  html, body {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #121212; /* Темный фон */
    font-family: 'Press Start 2P', cursive; /* Пиксельный шрифт */
  }

  body {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .prompt-container {
    width: 550px;
    height: 550px;
    max-width: 90vw;
    max-height: 90vh;
    
    background-color: #282828;
    border: 8px solid #FFD700; /* Ярко-желтая обводка */
    box-shadow: 10px 10px 0px #a18700; /* Жесткая тень */

    padding: 30px;
    box-sizing: border-box;

    display: flex;
    flex-direction: column;
    justify-content: space-between; /* Расталкивает верхний и нижний блоки */
    color: #E0E0E0;
    line-height: 1.8;
  }

  .prompt-header {
    font-size: 1.2em;
    color: #FFD700; /* Желтый акцент */
    text-align: left; /* <<< Выравнивание по левому краю */
    margin: 0;
  }

  .prompt-body {
    font-size: 1em;
    text-align: center; /* <<< Выравнивание по центру */
    white-space: pre-wrap;
    word-break: break-word;
    margin: 20px 0; /* Отступы для центрирования по вертикали */
  }

  .footer-text {
    text-align: right;
    font-size: 0.9em;
    color: #999;
    margin: 0;
  }

  .footer-text a {
    color: #FFD700; /* Желтый акцент */
    text-decoration: none;
  }
</style>
</head>
<body>

  <div class="prompt-container">
    
    <!-- Верхний блок: заголовок и сам промпт -->
    <div>
      <p class="prompt-header">Промпт:</p>
      <p class="prompt-body">[Здесь будет ваш промпт]</p>
    </div>
    
    <!-- Нижний блок: подпись -->
    <div>
      <p class="footer-text">
        по просьбе [NIK]<br>
        by <a href="https://t.me/UseShBot" target="_blank">@UseShBot</a>
      </p>
    </div>

  </div>

</body>
</html>